package com.oms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.oms.common.core.domain.PageQuery;
import com.oms.common.core.page.TableDataInfo;
import com.oms.common.exception.ServiceException;
import com.oms.common.utils.AssertTool;
import com.oms.common.utils.DataTool;
import com.oms.domain.PreSellerOrder;
import com.oms.domain.RuleLine;
import com.oms.domain.bo.*;
import com.oms.domain.vo.*;
import com.oms.manager.*;
import com.oms.mapper.RuleLineMapper;
import com.oms.rulev2.enums.RuleDimEnum;
import com.oms.rulev2.enums.RuleEngineConstants;
import com.oms.rulev2.enums.RuleTypeEnum;
import com.oms.rulev2.model.ConditionResult;
import com.oms.rulev2.service.OrderAutoDispatchRuleService;
import com.oms.rulev2.service.OrderReportRuleCalculateService;
import com.oms.rulev2.service.request.OrderAutoDispatchRequest;
import com.oms.rulev2.service.request.OrderReportMatchRequest;
import com.oms.rulev2.service.request.param.OrderAutoDispatchLineParam;
import com.oms.rulev2.service.request.param.OrderReportMatchLineParam;
import com.oms.rulev2.service.response.RuleFinalResult;
import com.oms.rulev2.service.response.info.OrderAutoDispatchInfo;
import com.oms.rulev2.service.response.info.ReportOrderMatchInfo;
import com.oms.service.IPreSellerOrderService;
import com.oms.service.IRuleLineConditionService;
import com.oms.service.IRuleLineService;
import com.oms.service.IRuleResultService;
import io.netty.util.internal.ThrowableUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 规则行Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-09
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class RuleLineServiceImpl extends ServiceImpl<RuleLineMapper, RuleLine>  implements IRuleLineService {

    private final RuleLineMapper baseMapper;
    private final IRuleLineConditionService ruleLineConditionService;
    private final IRuleResultService ruleResultService;
    private final RuleHeadManager ruleHeadManager;
    private final RuleTypeManager ruleTypeManager;
    private final RuleDimensionManager ruleDimensionManager;
    private final RuleFieldManager ruleFieldManager;
    private final RuleDictionaryManager ruleDictionaryManager;
    private final RuleLineManager ruleLineManager;
    @Autowired
    @Lazy
    private IPreSellerOrderService preSellerOrderService;
    @Autowired
    @Lazy
    private OrderAutoDispatchRuleService orderAutoDispatchRuleService;
    @Autowired
    @Lazy
    private OrderReportRuleCalculateService orderReportRuleCalculateService;


    /**
     * 查询规则行
     */
    @Override
    public RuleLineVo queryById(Long id){
        RuleLineVo ruleLineVo = baseMapper.selectVoById(id);
        RuleLineBo bo = new RuleLineBo();
        bo.setIsQueryDetail(Boolean.TRUE);
        bo.setIsQueryConditionAndResult(Boolean.TRUE);
        fullInfo(ruleLineVo, bo);
        return ruleLineVo;
    }

    private void fullInfo(RuleLineVo ruleLineVo, RuleLineBo bo) {
        fullInfo(Collections.singletonList(ruleLineVo), bo);
    }

    /**
     * 查询规则行列表
     */
    @Override
    public TableDataInfo<RuleLineVo> queryPageList(RuleLineBo bo, PageQuery pageQuery) {
        // 根据规则类型code获取规则类型信息
        final RuleTypeVo ruleType = ruleTypeManager.getTypeByCode(bo.getRuleTypeCode());
        if (Objects.isNull(ruleType)) {
            throw new ServiceException("规则类型不存在");
        }
        bo.setRuleTypeId(ruleType.getId());
        // 规则项目
        if (Objects.nonNull(bo.getPrjId())) {
            RuleLineConditionBo query = new RuleLineConditionBo();
            query.setRuleTypeId(bo.getRuleTypeId());
            query.setDimCode(RuleDimEnum.DIM_PRJ_ID.getCode());
            query.setDimKey(bo.getPrjId().toString());
            query.setRuleLineId(bo.getId());
            List<RuleLineConditionVo> conditionVos = ruleLineConditionService.queryList(query);
            if (CollUtil.isEmpty(conditionVos)) {
                return TableDataInfo.build(new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize(), 0));
            }
            List<Long> ruleLineIds = conditionVos.stream()
                    .map(RuleLineConditionVo::getRuleLineId).distinct().collect(Collectors.toList());
            bo.setRuleLineIds(ruleLineIds);
        }

        // 配送供应商
        if (Objects.nonNull(bo.getVendorKey())) {
            RuleResultBo query = new RuleResultBo();
            query.setReturnKey(bo.getVendorKey());
            List<RuleResultVo> resultVos = ruleResultService.queryList(query);
            if (CollUtil.isEmpty(resultVos)) {
                return TableDataInfo.build(new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize(), 0));
            }
            List<Long> ruleLineIds = resultVos.stream()
                    .map(RuleResultVo::getRuleLineId).distinct().collect(Collectors.toList());
            bo.setRuleLineIds(ruleLineIds);
        }

        // 审核原因
        if (Objects.nonNull(bo.getAuditReasonKey())) {
            RuleResultBo query = new RuleResultBo();
            query.setReturnKey(bo.getAuditReasonKey());
            List<RuleResultVo> resultVos = ruleResultService.queryList(query);
            if (CollUtil.isEmpty(resultVos)) {
                return TableDataInfo.build(new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize(), 0));
            }
            List<Long> ruleLineIds = resultVos.stream()
                    .map(RuleResultVo::getRuleLineId).distinct().collect(Collectors.toList());
            bo.setRuleLineIds(ruleLineIds);
        }

        // 审核人
        if (Objects.nonNull(bo.getReviewedBy())) {
            RuleResultBo query = new RuleResultBo();
            query.setReturnVal(bo.getReviewedBy());
            List<RuleResultVo> resultVos = ruleResultService.queryList(query);
            if (CollUtil.isEmpty(resultVos)) {
                return TableDataInfo.build(new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize(), 0));
            }
            List<Long> ruleLineIds = resultVos.stream()
                    .map(RuleResultVo::getRuleLineId).distinct().collect(Collectors.toList());
            bo.setRuleLineIds(ruleLineIds);
        }

        LambdaQueryWrapper<RuleLine> lqw = buildQueryWrapper(bo);
        Page<RuleLineVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        fullInfo(result.getRecords(), bo);

        return TableDataInfo.build(result);
    }

    private void fullInfo(List<RuleLineVo> records, RuleLineBo bo) {
        if (CollUtil.isEmpty(records)) {
            return;
        }

        List<Long> ruleLineIds = records.stream().map(RuleLineVo::getId)
                .collect(Collectors.toList());
        // 取出规则匹配条件信息 获取规则对应的项目名称
        RuleLineConditionBo conditionQuery = new RuleLineConditionBo();
        conditionQuery.setRuleLineIds(ruleLineIds);
        List<RuleLineConditionVo> conditionList = ruleLineConditionService.queryList(conditionQuery);
        Map<Long,List<RuleLineConditionVo>> conditionMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(conditionList)){
            conditionMap = conditionList.stream().collect(Collectors.groupingBy(RuleLineConditionVo::getRuleLineId));
        }

        // 查询返回结果
        RuleResultBo resultQuery = new RuleResultBo();
        resultQuery.setRuleLineIds(ruleLineIds);
        List<RuleResultVo> resultList = ruleResultService.queryList(resultQuery);
        Map<Long,List<RuleResultVo>> resultMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(resultList)){
            resultMap = resultList.stream().collect(Collectors.groupingBy(RuleResultVo::getRuleLineId));
        }

        // 是否查询未添加的条件和返回结果
        if (Boolean.TRUE.equals(bo.getIsQueryConditionAndResult())) {
            fillNoAddCondAndResult(records, conditionMap, resultMap);
        }

        // 填充信息
        fullInfo(records, conditionMap, resultMap, bo);
    }

    private void fillNoAddCondAndResult(List<RuleLineVo> records, Map<Long, List<RuleLineConditionVo>> conditionMap, Map<Long, List<RuleResultVo>> resultMap) {
        final Set<Long> ruleTypeIdList = records.stream().map(RuleLineVo::getRuleTypeId).collect(Collectors.toSet());

        final Map<Long, List<RuleFieldVo>> ruleFieldMap = ruleFieldManager.getByRuleTypeIdNoCache(ruleTypeIdList);
        // records 根据规则行id分组
        Map<Long, RuleLineVo> ruleLineMap = records.stream().collect(Collectors.toMap(RuleLineVo::getId, Function.identity()));

        // 判断固定条件是否有不包含的规则字段
        for (Map.Entry<Long, List<RuleLineConditionVo>> condition : conditionMap.entrySet()) {
            final Long ruleLineId = condition.getKey();
            final RuleLineVo ruleLineVo = ruleLineMap.get(ruleLineId);
            final List<RuleFieldVo> ruleFieldVos = ruleFieldMap.get(ruleLineVo.getRuleTypeId());
            final List<RuleLineConditionVo> conditionVoList = condition.getValue();

            final List<RuleLineConditionVo> fixedConList = conditionVoList.stream()
                    .filter(item -> RuleEngineConstants.FILED_TYPE_CONDITION.getValue().equals(item.getFiledType()))
                    .collect(Collectors.toList());

            final List<RuleFieldVo> fixedFieldList = ruleFieldVos.stream()
                    .filter(item -> RuleEngineConstants.FILED_TYPE_CONDITION.getValue().equals(item.getFiledType()))
                    .collect(Collectors.toList());

            final List<Long> ruleLineFixedField = fixedConList.stream().map(RuleLineConditionVo::getRuleFieldId)
                    .collect(Collectors.toList());

            for (RuleFieldVo ruleField : fixedFieldList) {
                if (!ruleLineFixedField.contains(ruleField.getId())) {
                    RuleLineConditionVo ruleLineConditionVo = new RuleLineConditionVo();
                    ruleLineConditionVo.setRuleFieldId(ruleField.getId());
                    ruleLineConditionVo.setSort(ruleField.getSort());
                    conditionVoList.add(ruleLineConditionVo);
                }
            }
        }

        // 判断返回结果是否有不包含的规则字段
        for (Map.Entry<Long, List<RuleResultVo>> resultEntity : resultMap.entrySet()) {
            final Long ruleLineId = resultEntity.getKey();
            final RuleLineVo ruleLineVo = ruleLineMap.get(ruleLineId);
            final List<RuleFieldVo> ruleFieldVos = ruleFieldMap.get(ruleLineVo.getRuleTypeId());
            final List<RuleResultVo> resultVoList = resultEntity.getValue();

            final List<Long> ruleLineResultField = resultVoList.stream().map(RuleResultVo::getRuleFieldId)
                    .collect(Collectors.toList());

            final List<RuleFieldVo> resultFieldList = ruleFieldVos.stream()
                    .filter(item -> RuleEngineConstants.FILED_TYPE_RESULT.getValue().equals(item.getFiledType()))
                    .collect(Collectors.toList());
            for (RuleFieldVo ruleField : resultFieldList) {
                if (!ruleLineResultField.contains(ruleField.getId())) {
                    RuleResultVo ruleResultVo = new RuleResultVo();
                    ruleResultVo.setRuleFieldId(ruleField.getId());
                    ruleResultVo.setSort(ruleField.getSort());
                    resultVoList.add(ruleResultVo);
                }
            }
        }
    }

    /**
     * 填充规则列表信息
     *
     * @param records      规则列表
     * @param conditionMap 条件列表
     * @param resultMap    结果列表
     * @param bo
     */
    private void fullInfo(List<RuleLineVo> records, Map<Long, List<RuleLineConditionVo>> conditionMap, Map<Long, List<RuleResultVo>> resultMap, RuleLineBo bo) {
        for (RuleLineVo line : records) {
            if(conditionMap.containsKey(line.getId())){
                // 获取项目信息
                List<RuleLineConditionVo> conditions = conditionMap.get(line.getId());
                RuleLineConditionVo prjCondition = conditions
                        .stream().filter(l-> RuleDimEnum.DIM_PRJ_ID.getCode().equals(l.getDimCode())).findFirst().orElse(null);
                if (prjCondition != null) {
                    line.setPrjName(prjCondition.getDimValue());
                }
                final List<RuleResultVo> ruleResultVos = resultMap.get(line.getId());
                if (CollUtil.isNotEmpty(ruleResultVos)) {
                    // 获取配送供应商信息
                    RuleResultVo vendorResult = ruleResultVos
                            .stream().filter(l-> RuleDimEnum.DIM_VENDOR.getCode().equals(l.getDimCode())).findFirst().orElse(null);
                    if (vendorResult != null) {
                        line.setVendorName(vendorResult.getResultName());
                        line.setVendorKey(vendorResult.getResultValue());
                    }
                    // 获取费率信息
                    RuleResultVo rateResult = ruleResultVos
                            .stream().filter(l-> RuleDimEnum.DIM_RATE.getCode().equals(l.getDimCode())).findFirst().orElse(null);
                    if (rateResult != null) {
                        line.setDimRate(rateResult.getResultValue());
                    }
                    // 审核原因
                    RuleResultVo auditResult = ruleResultVos
                            .stream().filter(l-> RuleDimEnum.DIM_AUDIT_REASON.getCode().equals(l.getDimCode())).findFirst().orElse(null);
                    if (auditResult != null) {
                        line.setAuditReason(auditResult.getResultName());
                    }

                    // 审核人
                    RuleResultVo reviewedBy = ruleResultVos
                            .stream().filter(l-> RuleDimEnum.DIM_REVIEWED_BY.getCode().equals(l.getDimCode())).findFirst().orElse(null);
                    if (reviewedBy != null) {
                        line.setReviewedBy(reviewedBy.getResultName());
                    }
                }
            }
        }

        // 处理规则类型名称
        DataTool.processRecords(records, ruleTypeManager.getMapCache(),
                RuleLineVo::getRuleTypeId,
                RuleLineVo::setRuleTypeName,
                RuleTypeVo::getTypeName);

        // 处理规则头名称
        DataTool.processRecords(records, ruleHeadManager.getMapCache(),
                RuleLineVo::getRuleId,
                RuleLineVo::setRuleHeadname,
                RuleHeadVo::getRuleName);

        // 判断是否查询详情
        if (Boolean.TRUE.equals(bo.getIsQueryDetail())) {
            Map<Long, RuleFieldVo> ruleFieldMap = ruleFieldManager.getMapCache();
            Map<Long, RuleDimensionVo> ruleDimMap = ruleDimensionManager.getMapCache();
            Map<Long, RuleDictionaryVo> ruleDicMap = ruleDictionaryManager.getMapCache();
            Map<String, List<RuleDictionaryVo>> ruleDicByTypeMap = ruleDicMap.values().stream().collect(Collectors.groupingBy(RuleDictionaryVo::getDicType));

            for (RuleLineVo line : records) {
                List<RuleLineConditionVo> conditions = conditionMap.get(line.getId());
                List<RuleResultVo> results = resultMap.get(line.getId());

                // 排除禁用的字段
                results = results.stream().filter(item -> ruleFieldMap.containsKey(item.getRuleFieldId()))
                        .collect(Collectors.toList());

                // 处理返回结果信息
                if (CollUtil.isNotEmpty(results)) {
                    for (RuleResultVo ruleResultVo : results) {
                        RuleFieldVo ruleField = ruleFieldMap.get(ruleResultVo.getRuleFieldId());
                        ruleResultVo.setDimKey(ruleResultVo.getReturnKey());
                        if (ruleField != null) {
                            ruleResultVo.setReturnType(ruleField.getReturnType());
                            ruleResultVo.setSort(ruleField.getSort());
                            ruleResultVo.setRequired(ruleField.getRequired());
                            RuleDimensionVo ruleDim = ruleDimMap.get(ruleField.getDimId());
                            if (ruleDim != null) {
                                // 规则维度名称
                                ruleResultVo.setDimName(ruleDim.getDimName());
                                ruleResultVo.setDimCode(ruleDim.getDimCode());
                                String dimValue = ruleDim.getDimValue();
                                // 维度属性类型：0 文本 1下拉,2 下拉多选
                                Long dimType = ruleDim.getDimType();
                                ruleResultVo.setDimType(dimType);

                                // 自动派单规则 固定返回 配送服务商文案改成配送服务商/供应商
                                if (RuleTypeEnum.ORDER_AUTO_DISPATCH_RULE.getCode().equals(line.getRuleTypeCode())
                                        && "dimVendor".equals(ruleDim.getDimCode())){
                                    ruleResultVo.setDimName("配送供应商");
                                }

                                if(RuleEngineConstants.DIM_TYPE_PULL_DOWN.getValue().equals(dimType) || RuleEngineConstants.DIM_TYPE_CHECK_BOX.getValue().equals(dimType)){
                                    // 文本 没有选择下拉数据  下拉 下拉多选 返回下拉数据
                                    if (ruleDicByTypeMap.containsKey(dimValue)){
                                        ruleResultVo.setSelectData(ruleDicByTypeMap.get(dimValue));
                                    }
                                }
                            }
                        }
                    }
                    List<RuleResultVo> resultList = results.stream().sorted(Comparator.comparing(RuleResultVo::getSort).reversed()).collect(Collectors.toList());
                    line.setResultList(resultList);
                }

                // 填充条件信息，排除禁用的字段
                conditions = conditions.stream().filter(item -> ruleFieldMap.containsKey(item.getRuleFieldId()))
                        .collect(Collectors.toList());
                for (RuleLineConditionVo condition : conditions) {
                    RuleFieldVo ruleField = ruleFieldMap.get(condition.getRuleFieldId());
                    if (ruleField != null) {
                        condition.setSort(ruleField.getSort());
                        condition.setRequired(ruleField.getRequired());
                        condition.setReturnType(ruleField.getReturnType());
                        condition.setFiledType(ruleField.getFiledType());
                        RuleDimensionVo ruleDim = ruleDimMap.get(ruleField.getDimId());
                        if (ruleDim != null) {
                            String dimValue = ruleDim.getDimValue();
                            // 规则维度名称
                            condition.setDimName(ruleDim.getDimName());
                            condition.setDimCode(ruleDim.getDimCode());
                            // 维度属性类型：0 文本 1下拉,2 下拉多选
                            Long dimType = ruleDim.getDimType();
                            condition.setDimType(dimType);
                            if(RuleEngineConstants.DIM_TYPE_PULL_DOWN.getValue().equals(dimType) || RuleEngineConstants.DIM_TYPE_CHECK_BOX.getValue().equals(dimType)){
                                // 文本 没有选择下拉数据  下拉 下拉多选 返回下拉数据
                                if (ruleDicByTypeMap.containsKey(dimValue)){
                                    condition.setSelectData(ruleDicByTypeMap.get(dimValue));
                                }
                            }
                        }
                    }
                }
                // 固定匹配条件
                List<RuleLineConditionVo> fixedConList = conditions.stream().filter(l -> Objects.equals(RuleEngineConstants.FILED_TYPE_CONDITION.getValue(), l.getIsFixed())).collect(Collectors.toList());
                // 动态匹配条件
                List<RuleLineConditionVo> trendsConList = conditions.stream().filter(l -> Objects.equals(RuleEngineConstants.FILED_TYPE.getValue(), l.getIsFixed())).collect(Collectors.toList());
                line.setFixedConList(fixedConList);
                line.setTrendsConList(trendsConList);
            }
        }
    }

    /**
     * 查询规则行列表
     */
    @Override
    public List<RuleLineVo> queryList(RuleLineBo bo) {
        // 根据规则类型code获取规则类型信息
        if (StrUtil.isNotBlank(bo.getRuleTypeCode())) {
            final RuleTypeVo ruleType = ruleTypeManager.getTypeByCode(bo.getRuleTypeCode());
            if (Objects.isNull(ruleType)) {
                throw new ServiceException("规则类型不存在");
            }
            bo.setRuleTypeId(ruleType.getId());
        }
        LambdaQueryWrapper<RuleLine> lqw = buildQueryWrapper(bo);
        List<RuleLineVo> data = baseMapper.selectVoList(lqw);
        fullInfo(data, bo);
        return data;
    }

    private LambdaQueryWrapper<RuleLine> buildQueryWrapper(RuleLineBo ruleLineBo) {
        LambdaQueryWrapper<RuleLine> queryWrapper = Wrappers.lambdaQuery();

        // 基本条件
        addBasicConditions(queryWrapper, ruleLineBo);

        // 时间相关条件
        addTimeConditions(queryWrapper, ruleLineBo);

        // 状态相关条件
        addStatusConditions(queryWrapper, ruleLineBo);

        // 其他条件
        queryWrapper.in(CollUtil.isNotEmpty(ruleLineBo.getRuleLineIds()), RuleLine::getId, ruleLineBo.getRuleLineIds());

        return queryWrapper;
    }

    private void addBasicConditions(LambdaQueryWrapper<RuleLine> queryWrapper, RuleLineBo ruleLineBo) {
        queryWrapper.eq(ruleLineBo.getTenantId() != null, RuleLine::getTenantId, ruleLineBo.getTenantId())
                   .eq(ruleLineBo.getRuleTypeId() != null, RuleLine::getRuleTypeId, ruleLineBo.getRuleTypeId())
                   .eq(ruleLineBo.getRuleId() != null, RuleLine::getRuleId, ruleLineBo.getRuleId())
                   .eq(StringUtils.isNotBlank(ruleLineBo.getRuleCode()), RuleLine::getRuleCode, ruleLineBo.getRuleCode())
                   .like(StringUtils.isNotBlank(ruleLineBo.getRuleName()), RuleLine::getRuleName, ruleLineBo.getRuleName())
                   .eq(StringUtils.isNotBlank(ruleLineBo.getTagType()), RuleLine::getTagType, ruleLineBo.getTagType());
    }

    private void addTimeConditions(LambdaQueryWrapper<RuleLine> queryWrapper, RuleLineBo ruleLineBo) {
        queryWrapper.eq(Objects.nonNull(ruleLineBo.getValidityStartAt()), RuleLine::getValidityStartAt, ruleLineBo.getValidityStartAt())
                   .eq(Objects.nonNull(ruleLineBo.getValidityEndAt()), RuleLine::getValidityEndAt, ruleLineBo.getValidityEndAt())
                   .eq(ruleLineBo.getDeleteTime() != null, RuleLine::getDeleteTime, ruleLineBo.getDeleteTime());
    }

    private void addStatusConditions(LambdaQueryWrapper<RuleLine> queryWrapper, RuleLineBo ruleLineBo) {
        queryWrapper.eq(ruleLineBo.getRuleStatus() != null, RuleLine::getRuleStatus, ruleLineBo.getRuleStatus())
                   .eq(ruleLineBo.getPriority() != null, RuleLine::getPriority, ruleLineBo.getPriority())
                   .eq(ruleLineBo.getIsIndefinite() != null, RuleLine::getIsIndefinite, ruleLineBo.getIsIndefinite())
                   .eq(ruleLineBo.getOverall() != null, RuleLine::getOverall, ruleLineBo.getOverall())
                   .eq(ruleLineBo.getDeleted() != null, RuleLine::getDeleted, ruleLineBo.getDeleted())
                   .eq(ruleLineBo.getDeleteBy() != null, RuleLine::getDeleteBy, ruleLineBo.getDeleteBy())
                   .like(StringUtils.isNotBlank(ruleLineBo.getDeleteName()), RuleLine::getDeleteName, ruleLineBo.getDeleteName())
                   .like(StringUtils.isNotBlank(ruleLineBo.getCreateName()), RuleLine::getCreateName, ruleLineBo.getCreateName())
                   .like(StringUtils.isNotBlank(ruleLineBo.getUpdateName()), RuleLine::getUpdateName, ruleLineBo.getUpdateName());
    }

    /**
     * 新增规则行
     */
    @Override
    public Boolean insertByBo(RuleLineBo bo) {
        RuleLine add = BeanUtil.toBean(bo, RuleLine.class);
        // 判断状态
        if (bo.getRuleStatus() == null) {
            add.setRuleStatus(RuleEngineConstants.REVIEW.getValue());
        }
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改规则行
     */
    @Override
    public Boolean updateByBo(RuleLineBo bo) {
        RuleLine update = BeanUtil.toBean(bo, RuleLine.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(RuleLine entity){
        if (entity.getId() == null) {
            Objects.requireNonNull(entity, "规则行不能为空");
            if (StringUtils.isBlank(entity.getRuleCode())) {
                throw new IllegalArgumentException("规则编码不能为空");
            }
            if (StringUtils.isBlank(entity.getRuleName())) {
                throw new IllegalArgumentException("规则名称不能为空");
            }
            Objects.requireNonNull(entity.getRuleTypeId(), "规则类型ID不能为空");
            Objects.requireNonNull(entity.getRuleId(), "规则ID不能为空");

            // 检查规则编码是否唯一
            LambdaQueryWrapper<RuleLine> query = Wrappers.lambdaQuery();
            query.eq(RuleLine::getRuleCode, entity.getRuleCode())
                    .ne(entity.getId() != null, RuleLine::getId, entity.getId());
            if (baseMapper.exists(query)) {
                throw new ServiceException("规则编码已存在");
            }
        }
    }

    /**
     * 批量删除规则行
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public RuleLineVo queryRuleCreate(String ruleTypeCode) {
        RuleTypeVo ruleType = ruleTypeManager.getTypeByCode(ruleTypeCode);
        AssertTool.isFalse(Objects.isNull(ruleType), "规则类型不存在");
        RuleHeadVo ruleHeadVo = ruleHeadManager.get(ruleType.getRuleId());
        AssertTool.isFalse(Objects.isNull(ruleHeadVo), "规则头不存在");

        RuleLineVo vo = new RuleLineVo();
        vo.setRuleCode(RuleEngineConstants.ruleLineCode());
        vo.setRuleTypeId(ruleType.getId());
        vo.setRuleTypeCode(ruleType.getTypeCode());
        vo.setRuleTypeName(ruleType.getTypeName());
        vo.setRuleHeadname(ruleHeadVo.getRuleName());
        vo.setRuleHeadCode(ruleHeadVo.getRuleCode());
        vo.setRuleId(ruleHeadVo.getId());
        List<RuleFieldVo> fieldList = ruleFieldManager.getByRuleTypeIdNoCache(ruleType.getId());

        List<RuleFieldVo> fields = fieldList.stream().filter(field ->
                        RuleEngineConstants.FILED_TYPE_CONDITION.getValue().equals(field.getFiledType()
                                ) || RuleEngineConstants.FILED_TYPE_RESULT.getValue().equals(field.getFiledType()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(fields)) {
            Set<Long> dimIds = fields.stream().map(RuleFieldVo::getDimId).collect(Collectors.toSet());
            List<RuleDimensionVo> dimList = ruleDimensionManager.getListByKey(dimIds);
            Map<Long, RuleDimensionVo> ruleDimMap = dimList.stream().collect(Collectors.toMap(RuleDimensionVo::getId, c -> c));
            List<String> dimValueList = dimList.stream().map(RuleDimensionVo::getDimValue).collect(Collectors.toList());

            // 获取所有规则维度所对应的常量值
            List<RuleDictionaryVo> dictionarys = ruleDictionaryManager.getByDicTypeList(dimValueList);
            Map<String,List<RuleDictionaryVo>> ruleDictionaryMap = dictionarys.stream()
                    .collect(Collectors.groupingBy(RuleDictionaryVo::getDicType));
            // 固定匹配条件
            List<RuleLineConditionVo> fixedConList = new ArrayList<>();
            // 返回结果
            List<RuleResultVo> resultList = new ArrayList<>();

            // 固定条件
            List<RuleFieldVo> fixedRuleFieldList =
                    fields.stream().filter(r->
                            Objects.equals(RuleEngineConstants.FILED_TYPE_CONDITION.getValue(),r.getFiledType()))
                            .collect(Collectors.toList());
            for (RuleFieldVo field : fixedRuleFieldList) {
                RuleLineConditionVo vo1 = new RuleLineConditionVo();
                if (ruleDimMap.containsKey(field.getDimId()) && ruleDimMap.get(field.getDimId()) != null){
                    RuleDimensionVo dimensionDO = ruleDimMap.get(field.getDimId());
                    vo1.setDimCode(dimensionDO.getDimCode());
                    vo1.setDimName(dimensionDO.getDimName());
                    vo1.setDimType(dimensionDO.getDimType());
                    vo1.setRuleFieldId(field.getId());
                    vo1.setSort(field.getSort());
                    vo1.setRequired(field.getRequired());

                    String dimValue = dimensionDO.getDimValue();
                    // 维度属性类型：0 文本 1下拉,2 下拉多选
                    Long dimType = dimensionDO.getDimType();
                    if(dimType.equals(RuleEngineConstants.DIM_TYPE_PULL_DOWN.getValue()) || dimType.equals(RuleEngineConstants.DIM_TYPE_CHECK_BOX.getValue())){
                        // 文本 没有选择下拉数据  下拉 下拉多选 返回下拉数据
                        if (ruleDictionaryMap.containsKey(dimValue)){
                            vo1.setSelectData(ruleDictionaryMap.get(dimValue));
                        }
                    }
                    fixedConList.add(vo1);
                }
            }
            vo.setFixedConList(fixedConList);

            // 固定返回值
            List<RuleFieldVo> resultRuleFieldList =
                    fields.stream().filter(r-> Objects.equals(RuleEngineConstants.FILED_TYPE_RESULT.getValue(),r.getFiledType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(resultRuleFieldList)){
                for (RuleFieldVo r : resultRuleFieldList) {
                    RuleResultVo vo1 = new RuleResultVo();
                    if (ruleDimMap.containsKey(r.getDimId()) && ruleDimMap.get(r.getDimId()) != null){
                        RuleDimensionVo dimensionDO = ruleDimMap.get(r.getDimId());
                        vo1.setDimCode(dimensionDO.getDimCode());
                        vo1.setDimName(dimensionDO.getDimName());
                        vo1.setDimType(dimensionDO.getDimType());
                        vo1.setRuleFieldId(r.getId());
                        vo1.setSort(r.getSort());
                        vo1.setReturnType(r.getReturnType());
                        vo1.setRequired(r.getRequired());

                        // 自动派单规则 固定返回 配送服务商文案改成配送服务商/供应商
                        if (r.getRuleTypeId().equals(RuleTypeEnum.ORDER_AUTO_DISPATCH_RULE.getId()) && "dimVendor".equals(dimensionDO.getDimCode())){
                            vo1.setDimName("配送供应商");
                        }

                        String dimValue = dimensionDO.getDimValue();
                        // 维度属性类型：0 文本 1下拉,2 下拉多选
                        Long dimType = dimensionDO.getDimType();
                        if(dimType.equals(RuleEngineConstants.DIM_TYPE_PULL_DOWN.getValue()) || dimType.equals(RuleEngineConstants.DIM_TYPE_CHECK_BOX.getValue())){
                            // 文本 没有选择下拉数据  下拉 下拉多选 返回下拉数据
                            if (ruleDictionaryMap.containsKey(dimValue)){
                                vo1.setSelectData(ruleDictionaryMap.get(dimValue));
                            }
                        }
                        resultList.add(vo1);
                    }
                }
                vo.setResultList(resultList);
            }
        }


        return vo;
    }

    @Override
    public List<RuleDimensionVo> queryMatchingCondition(String ruleTypeCode, Integer overall) {

        RuleTypeVo ruleType = ruleTypeManager.getTypeByCode(ruleTypeCode);
        AssertTool.isFalse(Objects.isNull(ruleType), "规则类型不存在");
        List<RuleDimensionVo> resultList = new ArrayList<>();
        // 获取默认匹配条件
        List<RuleFieldVo> fieldList = ruleFieldManager.getByRuleTypeId(ruleType.getId());
        List<RuleFieldVo> fields = fieldList.stream().filter(field ->
                        RuleEngineConstants.FILED_TYPE.getValue().equals(field.getFiledType()))
                .collect(Collectors.toList());
        if(CollUtil.isNotEmpty(fields)){
            Map<Long,RuleFieldVo> map = fields.stream().collect(Collectors.toMap(RuleFieldVo::getDimId,c->c));

            Set<Long> dimIds = fields.stream().map(RuleFieldVo::getDimId).collect(Collectors.toSet());
            List<RuleDimensionVo> dimList = ruleDimensionManager.getListByKey(dimIds);
            // 全局规则 不可筛选项目 因为项目没了 所以也不可选择客户分类 客户品牌
            if (overall != null && overall.equals(RuleEngineConstants.IS_OVERALL.getValue().intValue())){
                dimList = dimList.stream()
                        .filter(r-> !Objects.equals(r.getDimCode(), RuleDimEnum.DIM_PRJ_ID.getCode()))
                        .filter(r-> !Objects.equals(r.getDimCode(), RuleDimEnum.DIM_OUT_CATEGORY_ID.getCode()))
                        .filter(r-> !Objects.equals(r.getDimCode(), RuleDimEnum.DIM_OUT_BRAND_ID.getCode()))
                        .collect(Collectors.toList());
            }
            dimList.forEach(dim->{
                if (map.containsKey(dim.getId()) && map.get(dim.getId()) != null){
                    dim.setRuleFieldId(map.get(dim.getId()).getId());
                    dim.setSort(map.get(dim.getId()).getSort());
                }

                if (dim.getOperator() != null){
                    dim.setOperatorList(Arrays.asList(dim.getOperator().split(",")));
                }
            });
            // 根据sort排序返回结果
            resultList = dimList.stream().sorted(Comparator.comparing(RuleDimensionVo::getSort)
                    .reversed()).collect(Collectors.toList());
        }


        return resultList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveRule(RuleLineCreateBo bo) {
        try {
            return ruleLineManager.saveRule(bo);
        } catch (ServiceException e) {
            log.error("保存规则行失败 ServiceException {}", ThrowableUtil.stackTraceToString(e));
            throw e;
        } catch (Exception e) {
            log.error("保存规则行失败 其他异常 {}", ThrowableUtil.stackTraceToString(e));
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<RuleDictionaryVo> queryMatchingConditionDictionary(String dimValue) {
        return  ruleDictionaryManager.getByDicTypeList(Collections.singletonList(dimValue));
    }

    @Override
    public Boolean disabled(Long[] ids) {
        final RuleLine ruleLine = new RuleLine();
        ruleLine.setRuleStatus(RuleEngineConstants.DISABLE.getValue());
        return this.update(ruleLine,Wrappers.lambdaQuery(RuleLine.class).in(RuleLine::getId, ids));
    }

    @Override
    public List<RuleOrderMatchVo> queryRuleMatchingImitate(RuleMatchingBo bo) {
        if (StrUtil.isBlank(bo.getOrderId()) && StrUtil.isBlank(bo.getOutOrderId())) {
            throw new ServiceException("订单ID,外部订单号 不可同时为空");
        }
        // 查询预占单信息
        PreSellerOrder preSellerOrder = null;
        if (StrUtil.isNotBlank(bo.getOrderId())) {
            preSellerOrder = preSellerOrderService.lambdaQuery()
                    .select(PreSellerOrder::getId)
                    .eq(StrUtil.isNotBlank(bo.getOrderId()), PreSellerOrder::getId, Long.valueOf(bo.getOrderId())).one();
        } else {
            preSellerOrder = preSellerOrderService.lambdaQuery()
                    .select(PreSellerOrder::getId)
                    .eq(StrUtil.isNotBlank(bo.getOutOrderId()),PreSellerOrder::getOutId, bo.getOutOrderId()).one();
        }
        if (Objects.isNull(preSellerOrder)) {
            throw new ServiceException("订单不存在");
        }
        final PreSellerOrderVo preSellerOrderVo = preSellerOrderService.queryById(null, preSellerOrder.getId());
        // 组装信息，调用报备单规则和自动派单规则
        OrderReportMatchRequest orderReportMatchRequest = getOrderReportMatchRequest(preSellerOrderVo);
        //调用规则引擎接口..
        RuleFinalResult<List<ReportOrderMatchInfo>> ruleFinalResult = orderReportRuleCalculateService.calculation(orderReportMatchRequest);
        log.info("报备单规则返回结果：{}", JSONUtil.toJsonStr(ruleFinalResult));
        if(!ruleFinalResult.isSuccess()){
            log.error("调用规则引擎发生异常:{}", JSONUtil.toJsonStr(ruleFinalResult));
            throw new ServiceException("调用规则引擎发生异常");
        }
        final Map<Long, ReportOrderMatchInfo> reportOrderMatchMap = ruleFinalResult.getData()
                .stream().collect(Collectors.toMap(ReportOrderMatchInfo::getSkuId, Function.identity()));
        OrderAutoDispatchRequest orderAutoDispatchRequest = getOrderAutoDispatchRequest(preSellerOrderVo);
        RuleFinalResult<List<OrderAutoDispatchInfo>> listRuleFinalResult = orderAutoDispatchRuleService.calculation(orderAutoDispatchRequest);
        log.info("自动派单规则【非报备】返回结果：{}", JSONUtil.toJsonStr(listRuleFinalResult));
        if(!listRuleFinalResult.isSuccess()){
            log.error("调用规则引擎发生异常:{}", JSONUtil.toJsonStr(listRuleFinalResult));
            throw new ServiceException("调用规则引擎发生异常");
        }
        final Map<Long, OrderAutoDispatchInfo> orderAutoDispatchMap = listRuleFinalResult.getData()
                .stream().collect(Collectors.toMap(OrderAutoDispatchInfo::getSkuId, Function.identity()));

        // 将匹配的结果返回
        RuleOrderMatchVo result = new RuleOrderMatchVo();
        result.setOrderId(preSellerOrderVo.getId().toString());
        result.setOutOrderId(preSellerOrderVo.getOutId());
        result.setCreatedAt(preSellerOrderVo.getCreateTime());
        result.setDeliveryName("供应商直送");
        List<RuleOrderLineVo> ruleOrderLineInfos = new ArrayList<>();
        for (PreSellerOrderLineVo orderLine : preSellerOrderVo.getOrderLines()) {
            final ReportOrderMatchInfo reportOrderMatchInfo = reportOrderMatchMap.get(orderLine.getSkuId());
            RuleOrderLineVo ruleOrderLineVo = new RuleOrderLineVo();
            ruleOrderLineVo.setItemCode(orderLine.getItemId().toString());
            ruleOrderLineVo.setItemName(orderLine.getItemName());
            List<RuleOrderLineRuleVo> ruleOrderLineRuleVos = new ArrayList<>();
            if (reportOrderMatchInfo != null) {
                RuleOrderLineRuleVo ruleOrderLineRuleVo = new RuleOrderLineRuleVo();
                ruleOrderLineRuleVo.setRuleHeadName(reportOrderMatchInfo.getMatchRuleTypeName());
                ruleOrderLineRuleVo.setRuleCode(reportOrderMatchInfo.getMatchRuleCode());
                ruleOrderLineRuleVo.setRuleName(reportOrderMatchInfo.getMatchRuleName());
                ruleOrderLineRuleVo.setSort(reportOrderMatchInfo.getPriority());
                ruleOrderLineRuleVo.setMatchingStatus("已匹配");
                if (CollUtil.isNotEmpty(reportOrderMatchInfo.getConditionResults())) {
                    dimCodeToName(reportOrderMatchInfo.getConditionResults());
                    ruleOrderLineRuleVo.setMatchingCriteria(JSONUtil.toJsonStr(reportOrderMatchInfo.getConditionResults()));
                }
                ruleOrderLineRuleVos.add(ruleOrderLineRuleVo);
                ruleOrderLineVo.setRuleOrderLineRuleVos(ruleOrderLineRuleVos);
                ruleOrderLineInfos.add(ruleOrderLineVo);
                continue;

            }

            final OrderAutoDispatchInfo orderAutoDispatchInfo = orderAutoDispatchMap.get(orderLine.getSkuId());
            if (orderAutoDispatchInfo != null) {
                RuleOrderLineRuleVo ruleOrderLineRuleVo = new RuleOrderLineRuleVo();
                ruleOrderLineRuleVo.setRuleHeadName(orderAutoDispatchInfo.getMatchRuleTypeName());
                ruleOrderLineRuleVo.setRuleCode(orderAutoDispatchInfo.getMatchRuleCode());
                ruleOrderLineRuleVo.setRuleName(orderAutoDispatchInfo.getMatchRuleName());
                ruleOrderLineRuleVo.setSort(orderAutoDispatchInfo.getPriority());
                ruleOrderLineRuleVo.setMatchingStatus("已匹配");
                if (CollUtil.isNotEmpty(orderAutoDispatchInfo.getConditionResults())) {
                    dimCodeToName(orderAutoDispatchInfo.getConditionResults());
                    ruleOrderLineRuleVo.setMatchingCriteria(JSONUtil.toJsonStr(orderAutoDispatchInfo.getConditionResults()));
                }
                ruleOrderLineRuleVos.add(ruleOrderLineRuleVo);
                ruleOrderLineVo.setRuleOrderLineRuleVos(ruleOrderLineRuleVos);
                ruleOrderLineInfos.add(ruleOrderLineVo);
            }

        }
        result.setRuleOrderLineInfos(ruleOrderLineInfos);
        return CollUtil.newArrayList(result);
    }

    private void dimCodeToName(List<ConditionResult> conditionResults) {
        conditionResults.forEach(conditionResult -> {
            final RuleDimensionVo dim = ruleDimensionManager.getByCode(conditionResult.getName());
            if (dim != null) {
                conditionResult.setName(dim.getDimName());
            }
        });
    }

    private OrderAutoDispatchRequest getOrderAutoDispatchRequest(PreSellerOrderVo sellerOrderVo) {
        OrderAutoDispatchRequest orderAutoDispatchRequest = new OrderAutoDispatchRequest();
        orderAutoDispatchRequest.setPrjId(sellerOrderVo.getPrjId());
        orderAutoDispatchRequest.setTenantId(sellerOrderVo.getTenantId());
        // 没匹配报备单的
        List<OrderAutoDispatchLineParam> orderAutoDispatchLineParamList = new ArrayList<>();
        //循环所有的行
        for(PreSellerOrderLineVo sellerOrderLineVo : sellerOrderVo.getOrderLines()){
            OrderAutoDispatchLineParam orderAutoDispatchLineParam = new OrderAutoDispatchLineParam();
            orderAutoDispatchLineParam.setDimCustomerName(sellerOrderVo.getBuyerName());
            orderAutoDispatchLineParam.setDimInvoiceTitle(sellerOrderVo.getInvoiceTitle());
            orderAutoDispatchLineParam.setDimItemType(1L);
            orderAutoDispatchLineParam.setDimProvinceId(sellerOrderVo.getProvinceId());
            orderAutoDispatchLineParam.setDimCityId(sellerOrderVo.getCityId());
            orderAutoDispatchLineParam.setDimAreaId(sellerOrderVo.getRegionId());
            orderAutoDispatchLineParam.setDimReceiveUserName(sellerOrderVo.getReceiveUserName());
            orderAutoDispatchLineParam.setDimPurchaseCompany(sellerOrderVo.getPurchaseCompany());
            orderAutoDispatchLineParam.setDimCategoryId(String.valueOf(sellerOrderLineVo.getSkuCategoryId()));
            orderAutoDispatchLineParam.setDimBrandId(sellerOrderLineVo.getSkuBrandId());
            orderAutoDispatchLineParam.setDimSkuId(sellerOrderLineVo.getSkuId());
            orderAutoDispatchLineParamList.add(orderAutoDispatchLineParam);
        }
        orderAutoDispatchRequest.setOrderLines(orderAutoDispatchLineParamList);
        return orderAutoDispatchRequest;
    }

    private OrderReportMatchRequest getOrderReportMatchRequest(PreSellerOrderVo sellerOrder) {
        OrderReportMatchRequest orderReportMatchRequest = new OrderReportMatchRequest();
        orderReportMatchRequest.setPrjId(sellerOrder.getPrjId());
        orderReportMatchRequest.setTenantId(sellerOrder.getTenantId());
        orderReportMatchRequest.setOutOrderNo(sellerOrder.getOutId());
        orderReportMatchRequest.setApplyCode(sellerOrder.getApplyCode());
        orderReportMatchRequest.setPurchaseCompany(sellerOrder.getPurchaseCompany());
        orderReportMatchRequest.setReceiveUserMobile(sellerOrder.getReceiveMobile());
        orderReportMatchRequest.setReceiveUserName(sellerOrder.getReceiveMobile());
        orderReportMatchRequest.setSourceId(sellerOrder.getSourceId());
        orderReportMatchRequest.setProvinceCode(sellerOrder.getProvinceId().toString());
        orderReportMatchRequest.setCityCode(sellerOrder.getCityId().toString());
        List<OrderReportMatchLineParam> orderLines = new ArrayList<>();
        sellerOrder.getOrderLines().forEach(sellerOrderLine -> {
            OrderReportMatchLineParam orderReportMatchLineParam = new OrderReportMatchLineParam();
            orderReportMatchLineParam.setSkuId(sellerOrderLine.getSkuId());
            orderLines.add(orderReportMatchLineParam);
        });
        orderReportMatchRequest.setOrderLines(orderLines);
        return orderReportMatchRequest;
    }
}
